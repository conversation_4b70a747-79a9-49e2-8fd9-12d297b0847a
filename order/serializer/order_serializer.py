from rest_framework import serializers
from django.contrib.auth import get_user_model
from ..models import Order, OrderImage
from crop.models import Crop, CropType
from shipping.models import Shipping, ShippingCompany
from payment.models import Payment

User = get_user_model()


class OrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = '__all__'


class OrderImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderImage
        fields = '__all__'


# Admin-specific serializers for order management
class BuyerDetailSerializer(serializers.ModelSerializer):
    """Serializer for buyer information in order details"""
    class Meta:
        model = User
        fields = [
            'id',
            'fullname',
            'email',
            'phone',
            'role',
            'governorate',
            'address'
        ]


class CropTypeDetailSerializer(serializers.ModelSerializer):
    """Serializer for crop type information"""
    class Meta:
        model = CropType
        fields = ['id', 'crop_name', 'description']


class CropDetailSerializer(serializers.ModelSerializer):
    """Serializer for crop information in order details"""
    crop_type = CropTypeDetailSerializer(read_only=True)
    farmer = BuyerDetailSerializer(read_only=True)

    class Meta:
        model = Crop
        fields = [
            'id',
            'farmer',
            'crop_type',
            'quantity',
            'unit',
            'price',
            'harvest_date',
            'description',
            'status'
        ]


class ShippingCompanyDetailSerializer(serializers.ModelSerializer):
    """Serializer for shipping company information"""
    class Meta:
        model = ShippingCompany
        fields = ['id', 'shipper_name']


class ShippingDetailSerializer(serializers.ModelSerializer):
    """Serializer for shipping information in order details"""
    compid = ShippingCompanyDetailSerializer(read_only=True)

    class Meta:
        model = Shipping
        fields = [
            'id',
            'compid',
            'tracking_number',
            'estimated_delivery',
            'actual_delivery',
            'shipping_status',
            'created_at',
            'updated_at'
        ]


class PaymentDetailSerializer(serializers.ModelSerializer):
    """Serializer for payment information"""
    class Meta:
        model = Payment
        fields = [
            'id',
            'amount',
            'payment_status',
            'hold_date',
            'transfer_date',
            'created_at',
            'updated_at'
        ]


class UnpaidOrderDetailSerializer(serializers.ModelSerializer):
    """Comprehensive serializer for unpaid order details (admin only)"""
    buyer = BuyerDetailSerializer(read_only=True)
    crop = CropDetailSerializer(read_only=True)
    shipping_details = serializers.SerializerMethodField()
    payment_details = serializers.SerializerMethodField()
    completion_date = serializers.DateTimeField(source='updated_at', read_only=True)

    class Meta:
        model = Order
        fields = [
            'id',
            'buyer',
            'crop',
            'add_engineer_check',
            'quantity',
            'total_amount',
            'shipping_fee',
            'status',
            'completion_date',
            'shipping_details',
            'payment_details',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'completion_date', 'created_at', 'updated_at']

    def get_shipping_details(self, obj):
        """Get shipping information for the order"""
        try:
            shipping = Shipping.objects.get(orderid=obj, is_active=True, is_deleted=False)
            return ShippingDetailSerializer(shipping).data
        except Shipping.DoesNotExist:
            return None

    def get_payment_details(self, obj):
        """Get payment information for the order"""
        try:
            payment = Payment.objects.get(order=obj, is_active=True, is_deleted=False)
            return PaymentDetailSerializer(payment).data
        except Payment.DoesNotExist:
            return None