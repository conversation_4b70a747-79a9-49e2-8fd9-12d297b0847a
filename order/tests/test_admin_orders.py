from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from decimal import Decimal
from datetime import date, datetime

from order.models import Order
from crop.models import Crop, CropType
from payment.models import Payment
from shipping.models import Shipping, ShippingCompany

User = get_user_model()


class UnpaidOrdersAPITestCase(TestCase):
    """Test cases for admin unpaid orders endpoint"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create admin user
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Admin User',
            phone='1234567890',
            role='admin',
            governorate='Cairo',
            address='Admin Address',
            password='testpass123'
        )
        
        # Create regular user
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Regular User',
            phone='1234567891',
            role='user',
            governorate='Giza',
            address='User Address',
            password='testpass123'
        )
        
        # Create farmer
        self.farmer = User.objects.create_user(
            email='<EMAIL>',
            fullname='Farmer User',
            phone='1234567892',
            role='farmer',
            governorate='Alexandria',
            address='Farmer Address',
            password='testpass123'
        )
        
        # Create buyer
        self.buyer = User.objects.create_user(
            email='<EMAIL>',
            fullname='Buyer User',
            phone='1234567893',
            role='user',
            governorate='Luxor',
            address='Buyer Address',
            password='testpass123'
        )
        
        # Create crop type
        self.crop_type = CropType.objects.create(
            crop_name='Tomatoes',
            description='Fresh tomatoes'
        )
        
        # Create crop
        self.crop = Crop.objects.create(
            farmer=self.farmer,
            crop_type=self.crop_type,
            quantity=Decimal('100.00'),
            unit='kg',
            price=Decimal('50.00'),
            harvest_date=date.today(),
            description='High quality tomatoes',
            status='approved'
        )
        
        # Create shipping company
        self.shipping_company = ShippingCompany.objects.create(
            shipper_name='Fast Delivery Co.'
        )
        
        # Create completed order without payment (unpaid)
        self.unpaid_order = Order.objects.create(
            crop=self.crop,
            buyer=self.buyer,
            add_engineer_check=False,
            quantity=Decimal('50.00'),
            total_amount=Decimal('2500.00'),
            shipping_fee=Decimal('100.00'),
            status='completed'
        )
        
        # Create completed order with held payment (still unpaid)
        self.held_payment_order = Order.objects.create(
            crop=self.crop,
            buyer=self.buyer,
            add_engineer_check=True,
            quantity=Decimal('30.00'),
            total_amount=Decimal('1500.00'),
            shipping_fee=Decimal('75.00'),
            status='completed'
        )
        
        # Create payment with held status
        self.held_payment = Payment.objects.create(
            order=self.held_payment_order,
            amount=Decimal('1500.00'),
            payment_status='held',
            hold_date=datetime.now()
        )
        
        # Create completed order with transferred payment (paid)
        self.paid_order = Order.objects.create(
            crop=self.crop,
            buyer=self.buyer,
            add_engineer_check=False,
            quantity=Decimal('20.00'),
            total_amount=Decimal('1000.00'),
            shipping_fee=Decimal('50.00'),
            status='completed'
        )
        
        # Create payment with transferred status
        self.transferred_payment = Payment.objects.create(
            order=self.paid_order,
            amount=Decimal('1000.00'),
            payment_status='transferred',
            hold_date=datetime.now(),
            transfer_date=datetime.now()
        )
        
        # Create pending order (should not appear in results)
        self.pending_order = Order.objects.create(
            crop=self.crop,
            buyer=self.buyer,
            add_engineer_check=False,
            quantity=Decimal('25.00'),
            total_amount=Decimal('1250.00'),
            shipping_fee=Decimal('60.00'),
            status='pending_admin'
        )
        
        # Create shipping record for one of the orders
        self.shipping = Shipping.objects.create(
            orderid=self.unpaid_order,
            compid=self.shipping_company,
            tracking_number='TRK123456',
            estimated_delivery=date.today(),
            actual_delivery=date.today(),
            shipping_status='delivered'
        )
    
    def test_admin_can_access_unpaid_orders(self):
        """Test that admin can access unpaid orders list"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('order:admin-unpaid-orders')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('results', response.data)
        
        # Should contain unpaid_order and held_payment_order but not paid_order or pending_order
        order_ids = [order['id'] for order in response.data['results']]
        self.assertIn(self.unpaid_order.id, order_ids)
        self.assertIn(self.held_payment_order.id, order_ids)
        self.assertNotIn(self.paid_order.id, order_ids)
        self.assertNotIn(self.pending_order.id, order_ids)
    
    def test_non_admin_cannot_access_unpaid_orders(self):
        """Test that non-admin users cannot access unpaid orders"""
        self.client.force_authenticate(user=self.regular_user)
        url = reverse('order:admin-unpaid-orders')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_unauthenticated_access_denied(self):
        """Test that unauthenticated users cannot access unpaid orders"""
        url = reverse('order:admin-unpaid-orders')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_unpaid_orders_response_structure(self):
        """Test the structure of unpaid orders response"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('order:admin-unpaid-orders')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Check response structure
        self.assertIn('count', response.data)
        self.assertIn('next', response.data)
        self.assertIn('previous', response.data)
        self.assertIn('results', response.data)
        
        if response.data['results']:
            order = response.data['results'][0]
            
            # Check order fields
            required_fields = [
                'id', 'buyer', 'crop', 'quantity', 'total_amount',
                'shipping_fee', 'status', 'completion_date',
                'shipping_details', 'payment_details'
            ]
            for field in required_fields:
                self.assertIn(field, order)
            
            # Check buyer details
            buyer = order['buyer']
            buyer_fields = ['id', 'fullname', 'email', 'phone', 'role', 'governorate', 'address']
            for field in buyer_fields:
                self.assertIn(field, buyer)
            
            # Check crop details
            crop = order['crop']
            crop_fields = ['id', 'farmer', 'crop_type', 'quantity', 'unit', 'price']
            for field in crop_fields:
                self.assertIn(field, crop)
    
    def test_pagination_works(self):
        """Test that pagination works correctly"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('order:admin-unpaid-orders')
        
        # Test with limit parameter
        response = self.client.get(url, {'limit': 1})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertLessEqual(len(response.data['results']), 1)
    
    def test_orders_ordered_by_completion_date(self):
        """Test that orders are ordered by completion date (most recent first)"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('order:admin-unpaid-orders')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        if len(response.data['results']) > 1:
            # Check that orders are in descending order by completion_date
            dates = [order['completion_date'] for order in response.data['results']]
            self.assertEqual(dates, sorted(dates, reverse=True))
    
    def test_shipping_details_included(self):
        """Test that shipping details are included when available"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('order:admin-unpaid-orders')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Find the order with shipping details
        order_with_shipping = None
        for order in response.data['results']:
            if order['id'] == self.unpaid_order.id:
                order_with_shipping = order
                break
        
        self.assertIsNotNone(order_with_shipping)
        self.assertIsNotNone(order_with_shipping['shipping_details'])
        
        shipping = order_with_shipping['shipping_details']
        self.assertEqual(shipping['tracking_number'], 'TRK123456')
        self.assertEqual(shipping['shipping_status'], 'delivered')
    
    def test_payment_details_included(self):
        """Test that payment details are included when available"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('order:admin-unpaid-orders')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Find the order with payment details
        order_with_payment = None
        for order in response.data['results']:
            if order['id'] == self.held_payment_order.id:
                order_with_payment = order
                break
        
        self.assertIsNotNone(order_with_payment)
        self.assertIsNotNone(order_with_payment['payment_details'])
        
        payment = order_with_payment['payment_details']
        self.assertEqual(payment['payment_status'], 'held')
        self.assertEqual(float(payment['amount']), 1500.00)
    
    def test_no_unpaid_orders_response(self):
        """Test response when no unpaid orders exist"""
        # Mark all orders as paid or not completed
        Payment.objects.filter(order=self.unpaid_order).delete()
        Payment.objects.create(
            order=self.unpaid_order,
            amount=self.unpaid_order.total_amount,
            payment_status='transferred',
            hold_date=datetime.now(),
            transfer_date=datetime.now()
        )
        
        self.held_payment.payment_status = 'transferred'
        self.held_payment.transfer_date = datetime.now()
        self.held_payment.save()
        
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('order:admin-unpaid-orders')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['results']), 0)
        self.assertEqual(response.data['count'], 0)
