# Order Admin API Endpoints

This document describes the admin order endpoints implemented for the Django REST Framework backend.

## Overview

The order admin system allows administrators to monitor and manage orders, particularly focusing on completed orders that require payment processing. This endpoint helps admins identify orders that have been completed but haven't been paid yet.

## Endpoints

### Admin Endpoints

#### 1. Get Unpaid Delivered Orders (Admin Only)

**Endpoint**: `GET /api/order/admin/orders/unpaid/`

**Authorization**: Admin users only (users with role='admin')

**Description**: Retrieve all orders that have status "completed" but have not been paid yet.

**Query Parameters**:
- `page` (optional, integer): Page number for pagination
- `limit` (optional, integer): Number of results per page (default 20, max 100)

**Filtering Logic**:
Orders are considered unpaid if:
1. Order status is 'completed'
2. No payment record exists OR payment status is not 'transferred'

**Response Example**:
```json
{
    "success": true,
    "message": "Retrieved 2 unpaid completed orders",
    "count": 2,
    "next": null,
    "previous": null,
    "results": [
        {
            "id": 1,
            "buyer": {
                "id": 2,
                "fullname": "<PERSON>",
                "email": "<EMAIL>",
                "phone": "1234567890",
                "role": "user",
                "governorate": "Cairo",
                "address": "123 Main St"
            },
            "crop": {
                "id": 1,
                "farmer": {
                    "id": 3,
                    "fullname": "Farmer Smith",
                    "email": "<EMAIL>",
                    "phone": "0987654321",
                    "role": "farmer",
                    "governorate": "Giza",
                    "address": "Farm Road 1"
                },
                "crop_type": {
                    "id": 1,
                    "crop_name": "Tomatoes",
                    "description": "Fresh tomatoes"
                },
                "quantity": "100.00",
                "unit": "kg",
                "price": "50.00",
                "harvest_date": "2024-01-15",
                "description": "High quality tomatoes",
                "status": "approved"
            },
            "add_engineer_check": false,
            "quantity": "50.00",
            "total_amount": "2500.00",
            "shipping_fee": "100.00",
            "status": "completed",
            "completion_date": "2024-01-20T14:30:00Z",
            "shipping_details": {
                "id": 1,
                "compid": {
                    "id": 1,
                    "shipper_name": "Fast Delivery Co."
                },
                "tracking_number": "TRK123456",
                "estimated_delivery": "2024-01-22",
                "actual_delivery": "2024-01-21",
                "shipping_status": "delivered",
                "created_at": "2024-01-20T15:00:00Z",
                "updated_at": "2024-01-21T16:00:00Z"
            },
            "payment_details": null,
            "created_at": "2024-01-15T10:00:00Z",
            "updated_at": "2024-01-20T14:30:00Z"
        },
        {
            "id": 2,
            "buyer": {
                "id": 4,
                "fullname": "Jane Smith",
                "email": "<EMAIL>",
                "phone": "5555555555",
                "role": "user",
                "governorate": "Alexandria",
                "address": "456 Oak Ave"
            },
            "crop": {
                "id": 2,
                "farmer": {
                    "id": 3,
                    "fullname": "Farmer Smith",
                    "email": "<EMAIL>",
                    "phone": "0987654321",
                    "role": "farmer",
                    "governorate": "Giza",
                    "address": "Farm Road 1"
                },
                "crop_type": {
                    "id": 2,
                    "crop_name": "Potatoes",
                    "description": "Fresh potatoes"
                },
                "quantity": "200.00",
                "unit": "kg",
                "price": "30.00",
                "harvest_date": "2024-01-10",
                "description": "Organic potatoes",
                "status": "approved"
            },
            "add_engineer_check": true,
            "quantity": "75.00",
            "total_amount": "2250.00",
            "shipping_fee": "150.00",
            "status": "completed",
            "completion_date": "2024-01-19T12:15:00Z",
            "shipping_details": {
                "id": 2,
                "compid": {
                    "id": 2,
                    "shipper_name": "Quick Ship Ltd."
                },
                "tracking_number": "QS789012",
                "estimated_delivery": "2024-01-21",
                "actual_delivery": "2024-01-20",
                "shipping_status": "delivered",
                "created_at": "2024-01-19T13:00:00Z",
                "updated_at": "2024-01-20T17:30:00Z"
            },
            "payment_details": {
                "id": 1,
                "amount": "2250.00",
                "payment_status": "held",
                "hold_date": "2024-01-19T12:15:00Z",
                "transfer_date": null,
                "created_at": "2024-01-19T12:15:00Z",
                "updated_at": "2024-01-19T12:15:00Z"
            },
            "created_at": "2024-01-12T09:00:00Z",
            "updated_at": "2024-01-19T12:15:00Z"
        }
    ]
}
```

## Error Responses

### Authentication Required (401)
```json
{
    "detail": "Authentication credentials were not provided."
}
```

### Permission Denied (403)
```json
{
    "detail": "You do not have permission to perform this action."
}
```

### Server Error (500)
```json
{
    "success": false,
    "message": "An error occurred while retrieving unpaid orders: [error details]",
    "count": 0,
    "next": null,
    "previous": null,
    "results": []
}
```

## Integration Steps

### 1. Include URLs in Main Project

Add the order URLs to your main `urls.py` file:

```python
# mahsool/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/order/', include('order.urls')),
    # ... other URL patterns
]
```

### 2. Authentication Setup

Ensure your project has proper authentication middleware configured. The endpoint uses `IsAuthenticated` permission with additional admin role checking.

### 3. Database Migration

Run migrations to ensure all related models are created:

```bash
python manage.py makemigrations order payment shipping crop
python manage.py migrate
```

## Testing

Use the provided test file to verify the implementation:

```bash
python order/manual_test.py
```

For full integration testing with a database, create test data and use tools like Postman or curl to test the endpoint.

## Business Logic

### Order Payment Status Determination

An order is considered **unpaid** if:

1. **Order Status**: Must be 'completed'
2. **Payment Status**: One of the following conditions:
   - No payment record exists for the order
   - Payment record exists but status is 'held'
   - Payment record exists but status is 'ready'
   - Payment record exists but status is 'cancelled'

An order is considered **paid** if:
- Payment record exists AND status is 'transferred'

### Data Relationships

The endpoint provides comprehensive information by joining:
- **Order** → **Buyer** (User details)
- **Order** → **Crop** → **Farmer** (Crop and farmer details)
- **Order** → **Crop** → **CropType** (Crop type information)
- **Order** → **Shipping** → **ShippingCompany** (Shipping details)
- **Order** → **Payment** (Payment status and amounts)

## Security Considerations

1. **Admin Only Access**: Only users with `role='admin'` can access this endpoint
2. **Authentication Required**: All requests require valid authentication
3. **Data Privacy**: Sensitive information is properly serialized and filtered
4. **Error Handling**: Comprehensive error handling prevents information leakage

## Performance Considerations

1. **Optimized Queries**: Uses `select_related` and `prefetch_related` for efficient database queries
2. **Pagination**: Default pagination prevents large response sizes
3. **Filtering**: Database-level filtering reduces memory usage
4. **Indexing**: Consider adding database indexes on frequently queried fields:
   - `order.status`
   - `order.updated_at`
   - `payment.payment_status`

## Models Used

- **Order**: Main order information with status tracking
- **User**: Buyer and farmer information
- **Crop**: Product details and farmer relationship
- **CropType**: Product category information
- **Shipping**: Delivery tracking and company details
- **Payment**: Payment processing status and amounts
