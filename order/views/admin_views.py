from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth import get_user_model
from django.db.models import Q

from ..models import Order
from payment.models import Payment
from ..serializer.order_serializer import UnpaidOrderDetailSerializer

User = get_user_model()


class IsAdminUser(IsAuthenticated):
    """
    Custom permission to only allow admin users to access the view.
    """
    def has_permission(self, request, view):
        return (
            super().has_permission(request, view) and
            request.user.role == 'admin'
        )


class OrderPagination(PageNumberPagination):
    """
    Custom pagination for order endpoints
    """
    page_size = 20
    page_size_query_param = 'limit'
    max_page_size = 100
    page_query_param = 'page'


class UnpaidOrdersListView(generics.ListAPIView):
    """
    GET /api/order/admin/orders/unpaid
    
    Retrieve all orders that have status "completed" but have not been paid yet.
    Admin users only.
    
    Query Parameters:
    - page (optional, integer): Page number for pagination
    - limit (optional, integer): Number of results per page (default 20, max 100)
    
    Response: JSON object containing:
    - Paginated list of order objects with detailed information
    - Each order includes: order ID, buyer details, crop information, total amount, 
      shipping details, completion date, payment status
    - Success/failure status and message
    - Pagination metadata (count, next, previous)
    """
    serializer_class = UnpaidOrderDetailSerializer
    permission_classes = [IsAdminUser]
    pagination_class = OrderPagination
    
    def get_queryset(self):
        """
        Return completed orders that have not been paid yet.
        Orders are considered unpaid if:
        1. Order status is 'completed'
        2. No payment record exists OR payment status is not 'transferred'
        """
        # Get all completed orders
        completed_orders = Order.objects.filter(
            status='completed',
            is_active=True,
            is_deleted=False
        ).select_related(
            'buyer',
            'crop',
            'crop__farmer',
            'crop__crop_type'
        ).prefetch_related(
            'crop__images'
        )
        
        # Filter out orders that have been paid (payment status = 'transferred')
        unpaid_orders = []
        for order in completed_orders:
            try:
                payment = Payment.objects.get(
                    order=order,
                    is_active=True,
                    is_deleted=False
                )
                # If payment exists but is not transferred, it's still unpaid
                if payment.payment_status != 'transferred':
                    unpaid_orders.append(order.id)
            except Payment.DoesNotExist:
                # No payment record means unpaid
                unpaid_orders.append(order.id)
        
        # Return queryset filtered by unpaid order IDs, ordered by completion date (most recent first)
        return Order.objects.filter(
            id__in=unpaid_orders
        ).select_related(
            'buyer',
            'crop',
            'crop__farmer',
            'crop__crop_type'
        ).order_by('-updated_at')
    
    def list(self, request, *args, **kwargs):
        """
        Override list method to provide custom response format
        """
        try:
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                paginated_response = self.get_paginated_response(serializer.data)
                
                # Customize the response format
                return Response({
                    'success': True,
                    'message': f'Retrieved {len(serializer.data)} unpaid completed orders',
                    'count': paginated_response.data['count'],
                    'next': paginated_response.data['next'],
                    'previous': paginated_response.data['previous'],
                    'results': paginated_response.data['results']
                })
            
            serializer = self.get_serializer(queryset, many=True)
            return Response({
                'success': True,
                'message': f'Retrieved {len(serializer.data)} unpaid completed orders',
                'count': len(serializer.data),
                'next': None,
                'previous': None,
                'results': serializer.data
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'An error occurred while retrieving unpaid orders: {str(e)}',
                'count': 0,
                'next': None,
                'previous': None,
                'results': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
