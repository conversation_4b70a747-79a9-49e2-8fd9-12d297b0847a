from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from django.db import transaction

from ..models import UserVerificationStatus
from ..serializers.verification_serializer import (
    UnverifiedUserSerializer,
    UserVerificationDetailSerializer,
    VerificationStatusUpdateSerializer
)

User = get_user_model()


class IsAdminUser(IsAuthenticated):
    """
    Custom permission to only allow admin users to access the view.
    """
    def has_permission(self, request, view):
        return (
            super().has_permission(request, view) and
            request.user.role == 'admin'
        )


class VerificationPagination(PageNumberPagination):
    """
    Custom pagination for verification endpoints
    """
    page_size = 20
    page_size_query_param = 'limit'
    max_page_size = 100
    page_query_param = 'page'


class UnverifiedUsersListView(generics.ListAPIView):
    """
    GET /api/verification/admin/unverified-users
    
    Returns a paginated list of unverified users ordered by oldest first.
    Admin users only.
    
    Query Parameters:
    - page (optional, integer): Page number for pagination
    - limit (optional, integer): Number of results per page (max 100)
    """
    serializer_class = UnverifiedUserSerializer
    permission_classes = [IsAdminUser]
    pagination_class = VerificationPagination
    
    def get_queryset(self):
        """
        Return users who are not verified, ordered by registration date (oldest first)
        """
        return User.objects.filter(
            is_verified=False,
            is_active=True,
            is_deleted=False
        ).order_by('created_at')
    
    def list(self, request, *args, **kwargs):
        """
        Override list method to provide custom response format
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'message': 'Unverified users retrieved successfully',
            'data': serializer.data
        })


class UserVerificationDetailView(generics.RetrieveUpdateAPIView):
    """
    GET /api/verification/admin/users/{userId}/verification
    PATCH /api/verification/admin/users/{userId}/verification

    GET: Retrieve detailed user information and uploaded documents for verification review.
    PATCH: Approve or reject a user's verification request.
    Admin users only.

    Path Parameters:
    - userId (required, integer): The ID of the user to review/update

    PATCH Request Body:
    - status (required, string): Either "approved" or "rejected"
    - rejection_reason (optional, string): Required if status is "rejected"
    """
    permission_classes = [IsAdminUser]
    lookup_field = 'id'
    lookup_url_kwarg = 'userId'

    def get_queryset(self):
        """
        Return all users (admin can view any user's verification details)
        """
        return User.objects.filter(is_active=True, is_deleted=False)

    def get_serializer_class(self):
        """
        Return different serializers based on the HTTP method
        """
        if self.request.method == 'GET':
            return UserVerificationDetailSerializer
        elif self.request.method in ['PATCH', 'PUT']:
            return VerificationStatusUpdateSerializer
        return UserVerificationDetailSerializer

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve method to provide custom response format
        """
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)

            return Response({
                'success': True,
                'message': 'User verification details retrieved successfully',
                'data': serializer.data
            })
        except User.DoesNotExist:
            return Response({
                'success': False,
                'message': 'User not found',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)

    def update(self, request, *args, **kwargs):
        """
        Update user verification status
        """
        try:
            user = self.get_object()
            serializer = self.get_serializer(data=request.data)

            if serializer.is_valid():
                validated_data = serializer.validated_data
                new_status = validated_data['status']
                rejection_reason = validated_data.get('rejection_reason')

                with transaction.atomic():
                    # Get or create UserVerificationStatus
                    verification_status, created = UserVerificationStatus.objects.get_or_create(
                        user=user,
                        defaults={
                            'status': new_status,
                            'rejection_reason': rejection_reason
                        }
                    )

                    if not created:
                        # Update existing status
                        verification_status.status = new_status
                        verification_status.rejection_reason = rejection_reason
                        verification_status.save()

                    # Update user's is_verified field
                    if new_status == 'approved':
                        user.is_verified = True
                    else:
                        user.is_verified = False

                    user.save()

                # Return updated user details
                user_serializer = UserVerificationDetailSerializer(user)

                return Response({
                    'success': True,
                    'message': f'User verification status updated to {new_status}',
                    'data': user_serializer.data
                })

            else:
                return Response({
                    'success': False,
                    'message': 'Invalid data provided',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except User.DoesNotExist:
            return Response({
                'success': False,
                'message': 'User not found',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'An error occurred: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


