from rest_framework import serializers
from ..models import Verification , UserVerificationStatus

class VerificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Verification
        fields = [
            'id',
            'userid',
            'nationalid',
            'image_url',
            'type',
            'created_at',
            'updated_at',
            'is_active',
            'is_deleted',
        ]
        extra_kwargs = {
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True},
            'is_active': {'read_only': True},
            'is_deleted': {'read_only': True},
        }


class UserVerificationStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserVerificationStatus
        fields = ['status', 'rejection_reason']
        extra_kwargs = {
            'rejection_reason': {'required': False, 'allow_null': True}
        }
