from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from verification.models import Verification, UserVerificationStatus

User = get_user_model()


class AdminVerificationAPITestCase(TestCase):
    """Test cases for admin verification endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create admin user
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Admin User',
            phone='1234567890',
            role='admin',
            governorate='Cairo',
            address='Test Address',
            password='testpass123'
        )
        
        # Create regular users (unverified)
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            fullname='User One',
            phone='1234567891',
            role='user',
            governorate='Giza',
            address='User 1 Address',
            password='testpass123',
            is_verified=False
        )
        
        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            fullname='User Two',
            phone='1234567892',
            role='farmer',
            governorate='Alexandria',
            address='User 2 Address',
            password='testpass123',
            is_verified=False
        )
        
        # Create verified user (should not appear in unverified list)
        self.verified_user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Verified User',
            phone='1234567893',
            role='user',
            governorate='Luxor',
            address='Verified Address',
            password='testpass123',
            is_verified=True
        )
        
        # Create verification documents for user1
        self.verification1 = Verification.objects.create(
            userid=self.user1,
            nationalid='12345678901234',
            image_url='https://example.com/front.jpg',
            type='front'
        )
        
        self.verification2 = Verification.objects.create(
            userid=self.user1,
            nationalid='12345678901234',
            image_url='https://example.com/back.jpg',
            type='back'
        )
        
        # Create verification status for user2 (rejected)
        self.user2_status = UserVerificationStatus.objects.create(
            user=self.user2,
            status='rejected',
            rejection_reason='Invalid documents'
        )
    
    def test_unverified_users_list_admin_access(self):
        """Test that admin can access unverified users list"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('verification:admin-unverified-users')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('results', response.data)
        
        # Should contain user1 and user2 but not verified_user
        user_emails = [user['email'] for user in response.data['results']]
        self.assertIn('<EMAIL>', user_emails)
        self.assertIn('<EMAIL>', user_emails)
        self.assertNotIn('<EMAIL>', user_emails)
    
    def test_unverified_users_list_non_admin_access(self):
        """Test that non-admin users cannot access unverified users list"""
        self.client.force_authenticate(user=self.user1)
        url = reverse('verification:admin-unverified-users')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_unverified_users_list_unauthenticated(self):
        """Test that unauthenticated users cannot access unverified users list"""
        url = reverse('verification:admin-unverified-users')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_user_verification_detail_admin_access(self):
        """Test that admin can access user verification details"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('verification:admin-user-verification', kwargs={'userId': self.user1.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['id'], self.user1.id)
        self.assertEqual(response.data['data']['email'], '<EMAIL>')
        self.assertEqual(len(response.data['data']['verification_documents']), 2)
    
    def test_user_verification_detail_non_admin_access(self):
        """Test that non-admin users cannot access user verification details"""
        self.client.force_authenticate(user=self.user1)
        url = reverse('verification:admin-user-verification', kwargs={'userId': self.user2.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_update_user_verification_status_approve(self):
        """Test approving a user's verification"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('verification:admin-user-verification', kwargs={'userId': self.user1.id})
        
        data = {
            'status': 'approved'
        }
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('approved', response.data['message'])
        
        # Check that user is now verified
        self.user1.refresh_from_db()
        self.assertTrue(self.user1.is_verified)
        
        # Check verification status
        status_obj = UserVerificationStatus.objects.get(user=self.user1)
        self.assertEqual(status_obj.status, 'approved')
        self.assertIsNone(status_obj.rejection_reason)
    
    def test_update_user_verification_status_reject(self):
        """Test rejecting a user's verification"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('verification:admin-user-verification', kwargs={'userId': self.user1.id})
        
        data = {
            'status': 'rejected',
            'rejection_reason': 'Documents are not clear'
        }
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('rejected', response.data['message'])
        
        # Check that user is not verified
        self.user1.refresh_from_db()
        self.assertFalse(self.user1.is_verified)
        
        # Check verification status
        status_obj = UserVerificationStatus.objects.get(user=self.user1)
        self.assertEqual(status_obj.status, 'rejected')
        self.assertEqual(status_obj.rejection_reason, 'Documents are not clear')
    
    def test_update_user_verification_status_reject_without_reason(self):
        """Test rejecting without providing rejection reason should fail"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('verification:admin-user-verification', kwargs={'userId': self.user1.id})
        
        data = {
            'status': 'rejected'
        }
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('rejection_reason', response.data['errors'])
    
    def test_update_user_verification_status_invalid_status(self):
        """Test updating with invalid status should fail"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('verification:admin-user-verification', kwargs={'userId': self.user1.id})
        
        data = {
            'status': 'invalid_status'
        }
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
    
    def test_update_user_verification_status_non_admin(self):
        """Test that non-admin users cannot update verification status"""
        self.client.force_authenticate(user=self.user1)
        url = reverse('verification:admin-user-verification', kwargs={'userId': self.user2.id})
        
        data = {
            'status': 'approved'
        }
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_update_nonexistent_user_verification(self):
        """Test updating verification for non-existent user"""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('verification:admin-user-verification', kwargs={'userId': 99999})
        
        data = {
            'status': 'approved'
        }
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])
