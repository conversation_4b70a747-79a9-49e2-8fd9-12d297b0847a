#!/usr/bin/env python
"""
Manual test script for verification admin endpoints.
Run this script to test the API endpoints manually.
"""

import os
import sys
import django
from django.test import RequestFactory
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mahsool.settings')
sys.path.append('.')
django.setup()

from verification.views import UnverifiedUsersListView, UserVerificationDetailView
from verification.models import Verification, UserVerificationStatus

User = get_user_model()


def test_serializers():
    """Test that serializers work correctly"""
    print("Testing serializers...")

    from verification.serializers.verification_serializer import (
        UnverifiedUserSerializer,
        UserVerificationDetailSerializer,
        VerificationStatusUpdateSerializer,
        DocumentUploadSerializer,
        VerificationUploadSerializer,
        VerificationUploadResponseSerializer
    )

    # Test VerificationStatusUpdateSerializer validation
    serializer = VerificationStatusUpdateSerializer(data={'status': 'approved'})
    assert serializer.is_valid(), f"Serializer should be valid: {serializer.errors}"
    print("✓ Approved status validation passed")

    # Test rejection without reason (should fail)
    serializer = VerificationStatusUpdateSerializer(data={'status': 'rejected'})
    assert not serializer.is_valid(), "Rejection without reason should fail"
    assert 'rejection_reason' in serializer.errors, "Should require rejection reason"
    print("✓ Rejection validation passed")

    # Test rejection with reason (should pass)
    serializer = VerificationStatusUpdateSerializer(data={
        'status': 'rejected',
        'rejection_reason': 'Invalid documents'
    })
    assert serializer.is_valid(), f"Serializer should be valid: {serializer.errors}"
    print("✓ Rejection with reason validation passed")

    # Test DocumentUploadSerializer
    doc_serializer = DocumentUploadSerializer(data={
        'type': 'front',
        'image_url': 'https://example.com/front.jpg'
    })
    assert doc_serializer.is_valid(), f"Document serializer should be valid: {doc_serializer.errors}"
    print("✓ Document upload serializer validation passed")

    # Test VerificationUploadSerializer with valid data
    upload_serializer = VerificationUploadSerializer(data={
        'nationalid': '12345678901234',
        'documents': [
            {'type': 'front', 'image_url': 'https://example.com/front.jpg'},
            {'type': 'back', 'image_url': 'https://example.com/back.jpg'},
            {'type': 'photo', 'image_url': 'https://example.com/photo.jpg'}
        ]
    })
    assert upload_serializer.is_valid(), f"Upload serializer should be valid: {upload_serializer.errors}"
    print("✓ Verification upload serializer validation passed")

    # Test invalid national ID (too short)
    invalid_upload = VerificationUploadSerializer(data={
        'nationalid': '123',
        'documents': [{'type': 'front', 'image_url': 'https://example.com/front.jpg'}]
    })
    assert not invalid_upload.is_valid(), "Should fail with invalid national ID"
    assert 'nationalid' in invalid_upload.errors, "Should have national ID error"
    print("✓ Invalid national ID validation passed")

    # Test duplicate document types
    duplicate_upload = VerificationUploadSerializer(data={
        'nationalid': '12345678901234',
        'documents': [
            {'type': 'front', 'image_url': 'https://example.com/front1.jpg'},
            {'type': 'front', 'image_url': 'https://example.com/front2.jpg'}
        ]
    })
    assert not duplicate_upload.is_valid(), "Should fail with duplicate document types"
    assert 'documents' in duplicate_upload.errors, "Should have documents error"
    print("✓ Duplicate document types validation passed")

    print("All serializer tests passed!\n")


def test_views():
    """Test that views are properly configured"""
    print("Testing views...")

    factory = RequestFactory()

    # Test UnverifiedUsersListView
    view = UnverifiedUsersListView()
    assert hasattr(view, 'get_queryset'), "View should have get_queryset method"
    assert hasattr(view, 'list'), "View should have list method"
    print("✓ UnverifiedUsersListView structure is correct")

    # Test UserVerificationDetailView
    view = UserVerificationDetailView()
    assert hasattr(view, 'get_queryset'), "View should have get_queryset method"
    assert hasattr(view, 'retrieve'), "View should have retrieve method"
    assert hasattr(view, 'update'), "View should have update method"
    assert hasattr(view, 'get_serializer_class'), "View should have get_serializer_class method"
    print("✓ UserVerificationDetailView structure is correct")

    # Test VerificationUploadView
    from verification.views import VerificationUploadView
    upload_view = VerificationUploadView()
    assert hasattr(upload_view, 'create'), "Upload view should have create method"
    assert hasattr(upload_view, 'get_serializer'), "Upload view should have get_serializer method"
    print("✓ VerificationUploadView structure is correct")

    print("All view tests passed!\n")


def test_models():
    """Test that models are properly configured"""
    print("Testing models...")
    
    # Test model relationships
    assert hasattr(User, 'verifications'), "User should have verifications relationship"
    assert hasattr(User, 'verification_status'), "User should have verification_status relationship"
    print("✓ Model relationships are correct")
    
    # Test model choices
    status_choices = [choice[0] for choice in UserVerificationStatus.STATUS_CHOICES]
    assert 'pending' in status_choices, "Should have pending status"
    assert 'approved' in status_choices, "Should have approved status"
    assert 'rejected' in status_choices, "Should have rejected status"
    print("✓ UserVerificationStatus choices are correct")
    
    verification_choices = [choice[0] for choice in Verification.TYPE_CHOICES]
    assert 'front' in verification_choices, "Should have front type"
    assert 'back' in verification_choices, "Should have back type"
    assert 'photo' in verification_choices, "Should have photo type"
    print("✓ Verification type choices are correct")
    
    print("All model tests passed!\n")


def test_permissions():
    """Test permission classes"""
    print("Testing permissions...")

    from verification.views.admin_views import IsAdminUser, IsUserOrFarmer

    # Create mock request and user
    factory = RequestFactory()
    request = factory.get('/')

    # Test with admin user
    admin_user = User(role='admin')
    request.user = admin_user

    permission = IsAdminUser()
    # Note: This would require authentication, so we just test the structure
    assert hasattr(permission, 'has_permission'), "Permission should have has_permission method"
    print("✓ IsAdminUser permission class structure is correct")

    # Test IsUserOrFarmer permission
    user_permission = IsUserOrFarmer()
    assert hasattr(user_permission, 'has_permission'), "Permission should have has_permission method"
    print("✓ IsUserOrFarmer permission class structure is correct")

    print("All permission tests passed!\n")


def main():
    """Run all tests"""
    print("=" * 50)
    print("VERIFICATION ADMIN ENDPOINTS MANUAL TEST")
    print("=" * 50)
    print()
    
    try:
        test_serializers()
        test_views()
        test_models()
        test_permissions()
        
        print("=" * 50)
        print("🎉 ALL TESTS PASSED!")
        print("=" * 50)
        print()
        print("The verification endpoints are ready to use:")
        print()
        print("USER ENDPOINTS:")
        print("1. POST /api/verification/users/verification/ - Upload verification documents")
        print()
        print("ADMIN ENDPOINTS:")
        print("2. GET /api/verification/admin/unverified-users/ - List unverified users")
        print("3. GET /api/verification/admin/users/{userId}/verification/ - Get user details")
        print("4. PATCH /api/verification/admin/users/{userId}/verification/ - Update verification status")
        print()
        print("Make sure to:")
        print("- Include the verification app URLs in your main urls.py")
        print("- Ensure proper authentication middleware is configured")
        print("- Test with actual HTTP requests using a tool like Postman")
        print("- Users with role 'user' or 'farmer' can upload documents")
        print("- Only users with role 'admin' can access admin endpoints")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
