# Verification Admin API Endpoints

This document describes the three admin verification endpoints implemented for the Django REST Framework backend.

## Overview

The verification system allows administrators to review and approve/reject user verification requests. Users submit verification documents (ID front, back, and photo), and admins can review these documents and update the verification status.

## Endpoints

### 1. Get All Unverified Users (Admin Only)

**Endpoint**: `GET /api/verification/admin/unverified-users/`

**Authorization**: Admin users only

**Description**: Returns a paginated list of unverified users ordered by oldest first.

**Query Parameters**:
- `page` (optional, integer): Page number for pagination
- `limit` (optional, integer): Number of results per page (max 100, default 20)

**Response Example**:
```json
{
    "success": true,
    "message": "Unverified users retrieved successfully",
    "count": 25,
    "next": "http://localhost:8000/api/verification/admin/unverified-users/?page=2",
    "previous": null,
    "results": [
        {
            "id": 1,
            "fullname": "<PERSON>",
            "email": "<EMAIL>",
            "phone": "1234567890",
            "role": "user",
            "governorate": "Cairo",
            "address": "123 Main St",
            "registration_date": "2024-01-15T10:30:00Z",
            "verification_status": {
                "status": "pending",
                "rejection_reason": null
            },
            "is_verified": false
        }
    ]
}
```

### 2. Get User Verification Details (Admin Only)

**Endpoint**: `GET /api/verification/admin/users/{userId}/verification/`

**Authorization**: Admin users only

**Description**: Retrieve detailed user information and uploaded documents for verification review.

**Path Parameters**:
- `userId` (required, integer): The ID of the user to review

**Response Example**:
```json
{
    "success": true,
    "message": "User verification details retrieved successfully",
    "data": {
        "id": 1,
        "fullname": "John Doe",
        "email": "<EMAIL>",
        "phone": "1234567890",
        "role": "user",
        "governorate": "Cairo",
        "address": "123 Main St",
        "registration_date": "2024-01-15T10:30:00Z",
        "is_verified": false,
        "verification_status": {
            "status": "pending",
            "rejection_reason": null,
            "created_at": "2024-01-15T10:35:00Z",
            "updated_at": "2024-01-15T10:35:00Z"
        },
        "verification_documents": [
            {
                "id": 1,
                "userid": 1,
                "nationalid": "12345678901234",
                "image_url": "https://example.com/id_front.jpg",
                "type": "front",
                "created_at": "2024-01-15T10:35:00Z",
                "updated_at": "2024-01-15T10:35:00Z",
                "is_active": true,
                "is_deleted": false
            },
            {
                "id": 2,
                "userid": 1,
                "nationalid": "12345678901234",
                "image_url": "https://example.com/id_back.jpg",
                "type": "back",
                "created_at": "2024-01-15T10:36:00Z",
                "updated_at": "2024-01-15T10:36:00Z",
                "is_active": true,
                "is_deleted": false
            }
        ]
    }
}
```

### 3. Update User Verification Status (Admin Only)

**Endpoint**: `PATCH /api/verification/admin/users/{userId}/verification/`

**Authorization**: Admin users only

**Description**: Approve or reject a user's verification request.

**Path Parameters**:
- `userId` (required, integer): The ID of the user whose status to update

**Request Body**:
```json
{
    "status": "approved"  // or "rejected"
    "rejection_reason": "Optional reason if rejecting"
}
```

**Validation Rules**:
- `status` is required and must be either "approved" or "rejected"
- `rejection_reason` is required when status is "rejected"
- `rejection_reason` is ignored when status is "approved"

**Response Example (Approval)**:
```json
{
    "success": true,
    "message": "User verification status updated to approved",
    "data": {
        "id": 1,
        "fullname": "John Doe",
        "email": "<EMAIL>",
        // ... full user details with updated verification status
        "is_verified": true,
        "verification_status": {
            "status": "approved",
            "rejection_reason": null,
            "created_at": "2024-01-15T10:35:00Z",
            "updated_at": "2024-01-15T14:20:00Z"
        }
    }
}
```

**Response Example (Rejection)**:
```json
{
    "success": true,
    "message": "User verification status updated to rejected",
    "data": {
        "id": 1,
        "fullname": "John Doe",
        "email": "<EMAIL>",
        // ... full user details with updated verification status
        "is_verified": false,
        "verification_status": {
            "status": "rejected",
            "rejection_reason": "Documents are not clear enough",
            "created_at": "2024-01-15T10:35:00Z",
            "updated_at": "2024-01-15T14:20:00Z"
        }
    }
}
```

## Error Responses

### Authentication Required (401)
```json
{
    "detail": "Authentication credentials were not provided."
}
```

### Permission Denied (403)
```json
{
    "detail": "You do not have permission to perform this action."
}
```

### User Not Found (404)
```json
{
    "success": false,
    "message": "User not found",
    "data": null
}
```

### Validation Error (400)
```json
{
    "success": false,
    "message": "Invalid data provided",
    "errors": {
        "rejection_reason": ["Rejection reason is required when status is rejected."]
    }
}
```

## Integration Steps

### 1. Include URLs in Main Project

Add the verification URLs to your main `urls.py` file:

```python
# mahsool/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/verification/', include('verification.urls')),
    # ... other URL patterns
]
```

### 2. Authentication Setup

Ensure your project has proper authentication middleware configured. The endpoints use `IsAuthenticated` permission with additional admin role checking.

### 3. Database Migration

Run migrations to ensure the verification models are created:

```bash
python manage.py makemigrations verification
python manage.py migrate
```

## Testing

Use the provided test file to verify the implementation:

```bash
python verification/manual_test.py
```

For full integration testing with a database, create test users and use tools like Postman or curl to test the endpoints.

## Security Considerations

1. **Admin Only Access**: All endpoints require the user to have `role='admin'`
2. **Authentication Required**: All endpoints require valid authentication
3. **Input Validation**: All inputs are validated using Django REST Framework serializers
4. **Database Transactions**: Status updates use atomic transactions to ensure data consistency

## Models Used

- `User`: Custom user model with verification fields
- `Verification`: Stores verification documents (ID front, back, photo)
- `UserVerificationStatus`: Tracks verification status and rejection reasons
