from django.db import models
from django.conf import settings
from core.models import BaseModel

# Create your models here.
class Verification(BaseModel):
    TYPE_CHOICES = [
        ('front', 'Front'),
        ('back', 'Back'),
        ('photo', 'Photo'),
    ]

    userid = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='verifications'
    )
    nationalid = models.CharField(max_length=14)
    image_url = models.URLField()
    type = models.CharField(max_length=10, choices=TYPE_CHOICES)

    def __str__(self):
        return f"{self.user.fullname} - {self.type}"
    

class UserVerificationStatus(BaseModel):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='verification_status'
    )
    status = models.Char<PERSON>ield(max_length=20, choices=STATUS_CHOICES, default='pending')
    rejection_reason = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.user.fullname} - {self.status}"