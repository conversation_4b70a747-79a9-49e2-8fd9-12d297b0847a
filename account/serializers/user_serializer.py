from rest_framework import serializers
from django.contrib.auth import get_user_model

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            'userid',   #i think this should be id not userid
            'fullname',
            'email',
            'phone',
            'role',
            'governorate',
            'address',
            'is_verified',
            'is_email_verified',
            'is_banned',
            'created_at',
            'updated_at',
            'is_active',
            'is_deleted',
            'password',
        ]
        extra_kwargs = {
            'password': {'write_only': True},
            'is_verified': {'read_only': True},
            'is_email_verified': {'read_only': True},
            'is_banned': {'read_only': True},
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True},
            'is_active': {'read_only': True},
            'is_deleted': {'read_only': True},
        }
