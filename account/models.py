from django.contrib.auth.models import AbstractUser
from django.db import models
from core.models import BaseModel
from django.contrib.auth.models import AbstractUser

# Create your models here.
class User(AbstractUser, BaseModel):
    ROLE_CHOICES = [
        ('farmer', 'Farmer'),
        ('user', 'User'),
        ('admin', 'Admin'),
    ]

    username = None
    fullname = models.Char<PERSON>ield(max_length=255)
    phone = models.CharField(max_length=15, unique=True)
    email = models.EmailField(unique=True)
    role = models.Char<PERSON>ield(max_length=10, choices=ROLE_CHOICES)
    governorate = models.Char<PERSON>ield(max_length=100)
    address = models.Char<PERSON><PERSON>(max_length=255)
    is_verified = models.BooleanField(default=False)
    is_email_verified = models.BooleanField(default=False)
    is_banned = models.BooleanField(default=False)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['fullname']

    def __str__(self):
        return self.fullname
